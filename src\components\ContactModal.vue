<template>
  <div v-if="visible" class="contact-modal-overlay" @click="closeModal">
    <div class="contact-modal-content" @click.stop>
      <div class="contact-modal-header">
        <h2 class="contact-modal-title">联系我们</h2>
        <button class="contact-modal-close" @click="closeModal">&times;</button>
      </div>
      <div class="contact-modal-body">
        <img src="/images/联系我们.png" alt="联系我们" class="contact-image">
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContactModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    closeModal() {
      this.$emit('close');
    }
  }
}
</script>

<style scoped>
.contact-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  padding: 0;
  box-sizing: border-box;
}

.contact-modal-content {
  position: relative;
  width: 98vw;
  height: 98vh;
  max-width: 600px;
  max-height: 98vh;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.contact-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  min-height: 48px;
}

.contact-modal-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1c1c1c;
}

.contact-modal-close {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.contact-modal-close:hover {
  background: rgba(255, 255, 255, 1);
}

.contact-modal-body {
  flex: 1;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  min-height: 0;
}

.contact-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 0;
  display: block;
}

@media (max-width: 768px) {
  .contact-modal-content {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }
  .contact-modal-header {
    padding: 8px 8px;
    min-height: 40px;
  }
  .contact-modal-title {
    font-size: 1rem;
  }
  .contact-modal-close {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }
}
</style> 