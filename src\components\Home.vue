<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <div class="hero-left">
            <h1 class="hero-title">自动作业批改机</h1>
            <p class="hero-subtitle">我们的项目是由浙江大学博士团队开发的自动作业批改机，我们聚焦留痕批改与数据收集的核心需求，希望做一款用户热爱的产品。</p>
            <div class="hero-buttons">
              <button class="hero-card-button primary" @click="handleSignUp">
                联系我们
                <div class="arrow-container">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="rgb(59,130,246)" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14M12 5l7 7-7 7"/>
                  </svg>
                </div>
              </button>
              <button class="hero-card-button" @click="handleSignUpWithGoogle"><span>了解更多</span></button>
            </div>
          </div>
          <div class="hero-right">
            <div class="hero-video-placeholder">
              <div class="video-container">
                <video controls autoplay muted loop class="hero-video">
                  <source src="/videos/硬件演示视频.mp4" type="video/mp4">
                  您的浏览器不支持视频播放。
                </video>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 信任标识区域 -->
    <section class="trust-section">
      <div class="trust-container">
        <p class="trust-text">已在 14 所学校试点并留用，覆盖杭州、桐乡、上海、佛山、重庆、南京等多座城市的中小学，累计批改超过22万份试卷</p>
        <div class="logo-scroll">
          <div class="logo-container">
            <div class="logo-item">
              <img src="/images/logo1.png" alt="合作伙伴1" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo2.png" alt="合作伙伴2" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo3.png" alt="合作伙伴3" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo4.png" alt="合作伙伴4" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo5.png" alt="合作伙伴5" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo6.png" alt="合作伙伴6" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo7.png" alt="合作伙伴7" class="partner-logo">
            </div>
            <!-- 重复显示以实现无缝滚动 -->
            <div class="logo-item">
              <img src="/images/logo1.png" alt="合作伙伴1" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo2.png" alt="合作伙伴2" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo3.png" alt="合作伙伴3" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo4.png" alt="合作伙伴4" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo5.png" alt="合作伙伴5" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo6.png" alt="合作伙伴6" class="partner-logo">
            </div>
            <div class="logo-item">
              <img src="/images/logo7.png" alt="合作伙伴7" class="partner-logo">
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品特性展示 -->
    <section class="features-section">
      <div class="features-container">
        <h2 class="features-title">产品核心特性</h2>
        <div class="features-list">
          <div class="feature-item" ref="feature1">
            <div class="feature-content">
              <h3>深度化的软硬件融合</h3>
              <p>与理光打印机的省级代理深度合作，实现一体化的解决方案。</p>
            </div>
            <div class="feature-media">
              <div class="video-container">
                <video controls autoplay muted loop class="feature-video">
                  <source src="/videos/批改过程演示.mp4" type="video/mp4">
                  您的浏览器不支持视频播放。
                </video>
              </div>
            </div>
          </div>
          
          <div class="feature-item" ref="feature2">
            <div class="feature-content">
              <h3>极速高效的智能批改</h3>
              <p>依托 AI 核心算法，作业批改速度提升 90%，瞬间完成全批全改，大幅释放教师时间精力。</p>
            </div>
            <div class="feature-media">
              <div class="video-container">
                <video controls autoplay muted loop class="feature-video">
                  <source src="/videos/硬件集成演示.mp4" type="video/mp4">
                  您的浏览器不支持视频播放。
                </video>
              </div>
            </div>
          </div>
          
          <div class="feature-item" ref="feature3">
            <div class="feature-content">
              <h3>精准度超越人工的智能校验</h3>
              <p>使用最先进的大模型底座+题型优化，针对每种题型修改批改模式，极大提高了常见题型的批改准确率（客观题达到 99.7% 以上）。</p>
            </div>
            <div class="feature-media">
              <div class="video-container">
                <img src="/images/精准度演示.png" alt="精准度演示" class="feature-image">
              </div>
            </div>
          </div>
          
          <div class="feature-item" ref="feature4">
            <div class="feature-content">
              <h3>错题本自动生成系统</h3>
              <p>根据批改结果智能归类错题，自动生成个性化错题本，并关联知识点推荐强化练习。</p>
              <a
                href="/documents/6月5日整理高频率错题.pdf"
                download
                class="btn-primary download-btn"
                style="display:inline-block;margin-top:20px;"
              >下载错题本样例</a>
            </div>
            <div class="feature-media">
              <div class="video-container">
                <el-carousel 
                  :interval="4000" 
                  height="400px"
                  indicator-position="outside"
                  :autoplay="true"
                  class="mistake-carousel"
                >
                  <el-carousel-item v-for="(image, index) in mistakeImages" :key="index">
                    <img 
                      :src="image.src" 
                      :alt="image.alt" 
                      class="feature-image carousel-image"
                    >
                  </el-carousel-item>
                </el-carousel>
              </div>
            </div>
          </div>
          
          <div class="feature-item" ref="feature5">
            <div class="feature-content">
              <h3>多维度数据图表化分析</h3>
              <p>实时生成班级 / 年级成绩趋势图、知识点掌握热力图等可视化报表，一键导出学情分析报告。</p>
              <!--
              <a
                href="#"
                @click.prevent="showDownloadModal = true"
                class="btn-primary download-btn"
                style="display:inline-block;margin-top:20px;"
              >下载统计结果样例</a>
              -->
            </div>
            <div class="feature-media">
              <div class="video-container">
                <img src="/images/多维度数据图表化分析.png" alt="多维度数据图表化分析" class="feature-image">
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 价格方案 -->
    <section class="pricing-section">
      <div class="pricing-container">
        <h2 class="pricing-title">选择适合您的方案</h2>
        <div class="pricing-cards">
          <div class="pricing-card">
            <div class="card-header">
              <span class="card-type">基础版</span>
              <h3>小型学校</h3>
            </div>
            <div class="card-price">
              <span class="price">¥15,000</span>
              <span class="period">一次性支付</span>
            </div>
            <button class="card-button" @click="showContactModal = true">立即咨询</button>
            <ul class="card-features">
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 技术服务</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 15000 张 A3 作业正反批改算力</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 软件技术支持服务</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 单个学校首次使用支持</li>
            </ul>
          </div>

          <div class="pricing-card popular">
            <div class="popular-badge">推荐</div>
            <div class="card-header">
              <span class="card-type">标准版</span>
              <h3>中等规模学校</h3>
            </div>
            <div class="card-price">
              <span class="price">¥66,000</span>
              <span class="period">一次性支付</span>
            </div>
            <button class="card-button primary" @click="showContactModal = true">立即咨询</button>
            <ul class="card-features">
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> <b>打印机买断（理光 IMC 2510 + 高扫 + 定制模块）</b></li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 技术服务</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 软件技术支持服务</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 打印机耗材维护套餐（含15000 张 A3/8k 作业双面彩色打印耗材）</li>
            </ul>
          </div>

          <div class="pricing-card">
            <div class="card-header">
              <span class="card-type">租赁版</span>
              <h3>支持按年付费</h3>
            </div>
            <div class="card-price">
              <span class="price">¥30,000</span>
              <span class="period">每年支付</span>
            </div>
            <button class="card-button" @click="showContactModal = true">立即咨询</button>
            <ul class="card-features">
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 打印机租赁服务（理光 IMC 2510 + 高扫 + 定制模块）</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 3 年后资产归甲方</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 技术服务</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 软件技术支持服务</li>
              <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 打印机耗材维护套餐</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计展示区域 -->
    <section class="stats-section">
      <div class="stats-container">
        <h2 class="stats-title">系统运行统计</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M24 4L28.5 16.5H42L30.75 25.5L35.25 38L24 29L12.75 38L17.25 25.5L6 16.5H19.5L24 4Z" stroke="url(#gradient2)" stroke-width="2" fill="none"/>
                <circle cx="24" cy="24" r="8" stroke="url(#gradient2)" stroke-width="2" fill="none"/>
                <path d="M20 24L22 26L28 20" stroke="url(#gradient2)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                 <defs>
                   <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                     <stop offset="0%" style="stop-color:#60A5FA;stop-opacity:1" />
                     <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
                   </linearGradient>
                 </defs>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">
                {{ animatedRecordCount }}<span class="stat-unit">份</span>
              </div>
              <div class="stat-label">已批改试卷数量</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="8" y="8" width="32" height="32" rx="4" stroke="url(#gradient3)" stroke-width="2" fill="none"/>
                <path d="M16 16h16" stroke="url(#gradient3)" stroke-width="2"/>
                <path d="M16 24h16" stroke="url(#gradient3)" stroke-width="2"/>
                <path d="M16 32h12" stroke="url(#gradient3)" stroke-width="2"/>
                <circle cx="36" cy="12" r="4" fill="url(#gradient3)"/>
                <text x="36" y="16" text-anchor="middle" fill="white" font-size="8" font-weight="bold">R</text>
                                 <defs>
                   <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                     <stop offset="0%" style="stop-color:#93C5FD;stop-opacity:1" />
                     <stop offset="100%" style="stop-color:#60A5FA;stop-opacity:1" />
                   </linearGradient>
                 </defs>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">
                {{ formatRequestCount(animatedRequestCount) }}
              </div>
              <div class="stat-label">Request数量</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M24 4C12.954 4 4 12.954 4 24s8.954 20 20 20 20-8.954 20-20S35.046 4 24 4z" stroke="url(#gradient4)" stroke-width="2" fill="none"/>
                <path d="M24 12v12l8 8" stroke="url(#gradient4)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 24c0-6.627 5.373-12 12-12s12 5.373 12 12" stroke="url(#gradient4)" stroke-width="2" fill="none" stroke-dasharray="4 4"/>
                                 <defs>
                   <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                     <stop offset="0%" style="stop-color:#DBEAFE;stop-opacity:1" />
                     <stop offset="100%" style="stop-color:#93C5FD;stop-opacity:1" />
                   </linearGradient>
                 </defs>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">
                {{ formatTokenCount(animatedTokenCount) }}
              </div>
              <div class="stat-label">Token总用量</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- 下载选择弹窗 -->
  <div v-if="showDownloadModal" class="modal-overlay" @click="showDownloadModal = false">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>选择要下载的样例文件</h3>
        <button class="modal-close" @click="showDownloadModal = false">&times;</button>
      </div>
      <div class="modal-body">
        <div class="download-options">
          <label class="download-option">
            <input type="checkbox" v-model="downloadOptions.batchResult" />
            <span class="option-text">下载批改结果样例</span>
          </label>
          <label class="download-option">
            <input type="checkbox" v-model="downloadOptions.classStats" />
            <span class="option-text">下载班级统计结果样例</span>
          </label>
          <label class="download-option">
            <input type="checkbox" v-model="downloadOptions.gradeStats" />
            <span class="option-text">下载年级统计结果样例</span>
          </label>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" @click="showDownloadModal = false">取消</button>
        <button class="btn-primary" @click="downloadSelectedFiles">确认下载</button>
      </div>
    </div>
  </div>

  <!-- 联系弹窗 -->
  <ContactModal :visible="showContactModal" @close="showContactModal = false" />

  <!-- 售前咨询悬浮组件 -->
  <div class="presales-consultation">
    <div
      class="consultation-trigger"
      @mouseenter="showConsultationPopup = true"
      @mouseleave="showConsultationPopup = false"
    >
      <div class="consultation-icon">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
        </svg>
      </div>
      <div class="consultation-text">
        <span>售</span>
        <span>前</span>
        <span>咨</span>
        <span>询</span>
      </div>
    </div>

    <!-- 悬浮弹窗 -->
    <div
      v-show="showConsultationPopup"
      class="consultation-popup"
      @mouseenter="showConsultationPopup = true"
      @mouseleave="showConsultationPopup = false"
    >
      <div class="popup-header">
        <h3>AI 智能批改</h3>
        <p>即刻开启AI自动作业批改</p>
        <p>体验最前沿的AI</p>
      </div>
      <div class="popup-content">
        <img src="/images/联系我们.png" alt="联系我们" class="contact-image">
      </div>
    </div>
  </div>
</template>

<script>
import ContactModal from './ContactModal.vue'

export default {
  name: 'Home',
  components: {
    ContactModal
  },
  data() {
    return {
      activeFeature: 0,
      featureRefs: [],
      hoveredFeature: null,
      playingFeature: null,
      showDownloadModal: false,
      showContactModal: false,
      showConsultationPopup: false,
      downloadOptions: {
        batchResult: true,
        classStats: true,
        gradeStats: true
      },
      currentDataImage: '/images/250516_文三_英语_303_测试 _8k_p90_含原卷_00.png',
      currentDataImageAlt: '批改结果样例',
      imageTransitioning: false,
      currentImageIndex: 0,
      dataImages: [
        {
          src: '/images/250516_文三_英语_303_测试 _8k_p90_含原卷_00.png',
          alt: '批改结果样例'
        },
        {
          src: '/images/250516_文三_英语_303_测试 统计结果_00.png',
          alt: '统计结果样例'
        }
      ],
      carouselInterval: null,

      mistakeImages: [
        {
          src: '/images/6月5日整理高频率错题_00.png',
          alt: '错题本样例1'
        },
        {
          src: '/images/6月5日整理高频率错题_01.png',
          alt: '错题本样例2'
        },
        {
          src: '/images/6月5日整理高频率错题_02.png',
          alt: '错题本样例3'
        }
      ],

      // 统计数据
      statsData: {
        taskCount: 0,
        recordCount: 0
      },
      requestCount: 0,
      statsLoading: true,
      animatedRecordCount: 0,
      animatedRequestCount: 0,
      animatedTokenCount: 0
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initFeatureRefs();
      this.addScrollListener();
      this.addVideoListeners();
      this.addHoverListeners();
      this.startAutoCarousel();
      this.setActiveFeature(0);

      // 设置所有视频为1.5倍速，并监听 loadedmetadata
      const videos = document.querySelectorAll('video');
      videos.forEach(video => {
        const setRate = () => { video.playbackRate = 1.5; };
        video.addEventListener('loadedmetadata', setRate);
        setRate();
      });
      // 获取统计数据
      this.fetchStatsData();
    });
  },
  beforeUnmount() {
    this.removeScrollListener();
    this.removeVideoListeners();
    this.removeHoverListeners();
    this.stopAutoCarousel();

    clearInterval(this._animatedRecordCountTimer);
    clearInterval(this._animatedRequestCountTimer);
    clearInterval(this._animatedTokenCountTimer);
  },
  methods: {
    initFeatureRefs() {
      this.featureRefs = [
        this.$refs.feature1,
        this.$refs.feature2,
        this.$refs.feature3,
        this.$refs.feature4,
        this.$refs.feature5
      ];
    },
    addScrollListener() {
      window.addEventListener('scroll', this.handleScroll);
    },
    removeScrollListener() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    addVideoListeners() {
      // 为第一个特性项的视频添加播放状态监听
      const video = this.$refs.feature1?.querySelector('video');
      if (video) {
        video.addEventListener('play', () => this.setPlayingFeature(0));
        video.addEventListener('pause', () => this.setPlayingFeature(null));
        video.addEventListener('ended', () => this.setPlayingFeature(null));
      }
    },
    removeVideoListeners() {
      const video = this.$refs.feature1?.querySelector('video');
      if (video) {
        video.removeEventListener('play', () => this.setPlayingFeature(0));
        video.removeEventListener('pause', () => this.setPlayingFeature(null));
        video.removeEventListener('ended', () => this.setPlayingFeature(null));
      }
    },
    addHoverListeners() {
      this.featureRefs.forEach((ref, index) => {
        if (ref) {
          ref.addEventListener('mouseenter', () => this.setHoveredFeature(index));
          ref.addEventListener('mouseleave', () => this.setHoveredFeature(null));
        }
      });
    },
    removeHoverListeners() {
      this.featureRefs.forEach((ref, index) => {
        if (ref) {
          ref.removeEventListener('mouseenter', () => this.setHoveredFeature(index));
          ref.removeEventListener('mouseleave', () => this.setHoveredFeature(null));
        }
      });
    },
    setHoveredFeature(index) {
      this.hoveredFeature = index;
      this.updateFeatureStates();
    },
    setPlayingFeature(index) {
      this.playingFeature = index;
      this.updateFeatureStates();
    },
    updateFeatureStates() {
      this.featureRefs.forEach((ref, i) => {
        if (ref) {
          // 移除所有状态类
          ref.classList.remove('active', 'hovered', 'playing');
          
          // 添加相应的状态类
          if (this.playingFeature === i) {
            ref.classList.add('playing');
          } else if (this.hoveredFeature === i) {
            ref.classList.add('hovered');
          } else if (this.activeFeature === i) {
            ref.classList.add('active');
          }
        }
      });
    },
    handleScroll() {
      const featuresSection = document.querySelector('.features-section');
      if (!featuresSection) return;
      
      const sectionTop = featuresSection.offsetTop;
      const sectionHeight = featuresSection.offsetHeight;
      const scrollTop = window.pageYOffset;
      const windowHeight = window.innerHeight;
      
      // 检查是否在特性区域范围内
      if (scrollTop + windowHeight > sectionTop && scrollTop < sectionTop + sectionHeight) {
        const progress = (scrollTop + windowHeight - sectionTop) / (sectionHeight + windowHeight);
        const featureIndex = Math.floor(progress * this.featureRefs.length);
        this.setActiveFeature(Math.min(featureIndex, this.featureRefs.length - 1));
      }
    },
    setActiveFeature(index) {
      if (this.activeFeature !== index) {
        this.activeFeature = index;
        this.updateFeatureStates();
      }
    },
    goToLogin() {
      // 跳转到登录页面
      window.open('https://saomiaoshijuan.com/login', '_blank');
    },
    downloadSelectedFiles() {
      const files = [
        {
          name: '250516_文三_英语_303_测试 _8k_p90_含原卷.pdf',
          option: 'batchResult'
        },
        {
          name: '250516_文三_英语_303_测试 统计结果.pdf',
          option: 'classStats'
        },
        {
          name: '250515_文三_英语_301_多班级统计结果_美化版.xlsx',
          option: 'gradeStats'
        }
      ];

      // 下载选中的文件
      files.forEach(file => {
        if (this.downloadOptions[file.option]) {
          const link = document.createElement('a');
          link.href = `/documents/${file.name}`;
          link.download = file.name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      });

      // 关闭弹窗
      this.showDownloadModal = false;
    },
    switchImage(index) {
      this.currentImageIndex = index;
      this.imageTransitioning = true;
      setTimeout(() => {
        this.currentDataImage = this.dataImages[index].src;
        this.currentDataImageAlt = this.dataImages[index].alt;
        this.imageTransitioning = false;
      }, 500);
    },
    startAutoCarousel() {
      this.carouselInterval = setInterval(() => {
        const nextIndex = (this.currentImageIndex + 1) % this.dataImages.length;
        this.switchImage(nextIndex);
      }, 4000); // 每4秒切换一次
    },
    stopAutoCarousel() {
      if (this.carouselInterval) {
        clearInterval(this.carouselInterval);
        this.carouselInterval = null;
      }
    },

    handleSignUp() {
      // 跳转到联系我们页面
      this.$router.push('/contact');
    },
    handleSignUpWithGoogle() {
      // 占位符方法，点击了解更多按钮
      console.log('了解更多 button clicked');
    },

    // 获取统计数据
    async fetchStatsData() {
      this.statsLoading = true;
      try {
        // 写死统计数据
        this.statsData.recordCount = 224245; // 已批改试卷数量
        this.requestCount = 1246700; // Request数量 (124.67万)
        
        // 保留后端接口调用代码（注释掉，但保留以备后用）
        /*
        const correctResponse = await fetch('/api/docCorrectTask/correct/count', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });
        
        if (!correctResponse.ok) {
          throw new Error(`试卷统计请求失败: ${correctResponse.status}`);
        }
        
        const correctData = await correctResponse.json();
        
        if (correctData.code === 200) {
          this.statsData.taskCount = correctData.data.taskCount || 0;
          this.statsData.recordCount = correctData.data.recordCount || 0;
          // 强制更新视图
          this.$forceUpdate();
        }

        const requestResponse = await fetch('/api/gptAskLog/requestCount', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });
        
        if (!requestResponse.ok) {
          throw new Error(`Request统计请求失败: ${requestResponse.status}`);
        }
        
        const requestData = await requestResponse.json();
        
        if (requestData.code === 200) {
          this.requestCount = parseInt(requestData.data) || 0;
        }
        */
      } finally {
        this.statsLoading = false;
        this.animateNumber('animatedRecordCount', this.statsData.recordCount || 0, 800);
        this.animateNumber('animatedRecordCount', this.statsData.recordCount || 0, 800);
        this.animateNumber('animatedRequestCount', this.requestCount || 0, 800);
        this.animateNumber('animatedTokenCount', 8727000000, 800); // 87.27亿
      }
    },
    // 格式化Request数量显示
    formatRequestCount(count) {
      if (!count) return '0';
      const wanCount = count / 10000;
      return wanCount.toFixed(2) + ' 万';
    },
    // 格式化Token总用量显示
    formatTokenCount(tokenCount) {
      const yiCount = tokenCount / 100000000;
      return yiCount.toFixed(2) + ' 亿';
    },
    animateNumber(key, target, duration = 800, onStep) {
      const start = 0;
      const stepCount = 30;
      const step = (target - start) / stepCount;
      let current = start;
      let count = 0;
      clearInterval(this[`_${key}Timer`]);
      this[key] = start;
      this[`_${key}Timer`] = setInterval(() => {
        count++;
        current += step;
        let val = count >= stepCount ? target : Math.round(current);
        this[key] = val;
        if (onStep) onStep(val);
        // 日志：如果是animatedRequestCount动画，打印Request和Token
        if (key === 'animatedRequestCount') {
          console.log('Request:', val, 'Token:', val * 7000);
        }
        if (count >= stepCount) {
          clearInterval(this[`_${key}Timer`]);
        }
      }, duration / stepCount);
    }
  }
}
</script>

<style scoped>
.home-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #1c1c1c;
}

/* 英雄区域 */
.hero-section {
  background: rgba(96,166,255, 0.05);
  padding: 10px 0;
  min-height: 30vh;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  text-align: left;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 8px;
  color: #1c1c1c;
  text-align: left;
}

.hero-subtitle-main {
  font-size: 3rem;
  color: #6c757d;
  margin-bottom: 8px;
  line-height: 1.6;
  text-align: left;
}

.hero-subtitle {
  font-size: 1.7rem;
  color: #6c757d;
  margin-bottom: 32px;
  line-height: 1.6;
  text-align: left;
}

.hero-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}

.btn-primary {
  background: rgb(59,130,246);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  width: 140px;
}

.btn-primary:hover {
  background: rgb(37,99,235);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: white;
  color: #1c1c1c;
  border: 2px solid #dee2e6;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  min-width: 140px;
  width: 140px;
}

.btn-secondary:hover {
  border-color: #adb5bd;
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.btn-secondary:active {
  transform: translateY(0);
}

.btn-secondary span {
  margin-left: 0;
}

.hero-card-button {
  padding: 16px 32px;
  border: 2px solid rgb(96,166,255);
  background: transparent;
  color: rgb(96,166,255);
  border-radius: 24px;
  font-size: 1.3rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  min-width: 160px;
}

.hero-card-button:hover {
  background: rgb(96,166,255);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(96,166,255, 0.3);
}

.hero-card-button:active {
  transform: translateY(0);
}

.hero-card-button.primary {
  background: linear-gradient(135deg, rgb(147,197,253) 0%, rgb(59,130,246) 30%, rgb(29,78,216) 70%, rgb(30,58,138) 100%);
  background-size: 300% 300%;
  color: white;
  border: none;
  position: relative;
  animation: gradientShift 4s ease-in-out infinite;
}

.hero-card-button.primary::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgb(147,197,253) 0%, rgb(59,130,246) 30%, rgb(29,78,216) 70%, rgb(30,58,138) 100%);
  background-size: 300% 300%;
  border-radius: 26px;
  z-index: -1;
  animation: gradientShift 4s ease-in-out infinite;
}

.hero-card-button.primary:hover {
  background: linear-gradient(135deg, rgb(165,180,252) 0%, rgb(99,102,241) 25%, rgb(67,56,202) 50%, rgb(55,48,163) 75%, rgb(49,46,129) 100%);
  background-size: 400% 400%;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59,130,246, 0.5);
  animation: gradientShiftHover 3s ease-in-out infinite;
}

.hero-card-button.primary:hover::before {
  background: linear-gradient(135deg, rgb(165,180,252) 0%, rgb(99,102,241) 25%, rgb(67,56,202) 50%, rgb(55,48,163) 75%, rgb(49,46,129) 100%);
  background-size: 400% 400%;
  animation: gradientShiftHover 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

@keyframes gradientShiftHover {
  0% {
    background-position: 0% 0%;
  }
  20% {
    background-position: 100% 0%;
  }
  40% {
    background-position: 100% 100%;
  }
  60% {
    background-position: 0% 100%;
  }
  80% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 0% 0%;
  }
}

.hero-card-button span {
  margin-left: 0;
}

.arrow-container {
  width: 28px;
  height: 28px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-disclaimer {
  font-size: 0.875rem;
  color: #6c757d;
  line-height: 1.5;
}

.hero-video-placeholder {
  width: 100%;
  height: 400px;
  background: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 2px dashed #dee2e6;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
  background: #000;
}

.feature-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 16px;
  background: #f8f9fa;
  max-height: 410px;
}

.feature-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 16px;
  background: #f8f9fa;
  max-height: 410px;
}

.video-placeholder {
  color: #6c757d;
  font-size: 1.1rem;
}

/* 信任标识区域 */
.trust-section {
  padding: 60px 0;
  background: white;
}

.trust-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.trust-text {
  font-size: 1.4rem;
  color: rgb(96,166,255);
  margin-bottom: 40px;
}

.logo-scroll {
  width: 100%;
  overflow: hidden;
  position: relative;
  background: #fff;
  margin: 0 auto;
  padding: 16px 0;
}

.logo-container {
  display: flex;
  width: max-content;
  animation: logo-scroll-anim 18s linear infinite;
}

.logo-item {
  margin: 0 12px;
  min-width: 120px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  padding: 8px;
}

.partner-logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

@keyframes logo-scroll-anim {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 产品特性展示 */
.features-section {
  padding: 30px 0;
  background: white;
}

.features-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.features-title {
  text-align: center;
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 100px;
  color: #1c1c1c;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.feature-item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  opacity: 0.4;
  transform: translateY(30px);
  transition: all 0.6s ease;
  padding: 10px 0;
  cursor: pointer;
}

.feature-item.active {
  opacity: 1;
  transform: translateY(0);
}

.feature-item.hovered {
  opacity: 1;
  transform: translateY(0);
}

.feature-item.playing {
  opacity: 1;
  transform: translateY(0);
}

.feature-item:nth-child(even) {
  /* 移除RTL方向，保持文字在左侧 */
}

.feature-item:nth-child(even) .feature-content {
  /* 移除LTR方向设置 */
}

.feature-content h3 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
  color: #1c1c1c;
}

.feature-content p {
  font-size: 1.7rem;
  color: #6c757d;
  line-height: 1.6;
  text-align: left;
}

.feature-media {
  width: 100%;
  height: 450px;
  background: white;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #dee2e6;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 20px;
}

.feature-item.active .feature-media {
  box-shadow: 0 16px 48px rgba(96, 166, 255, 0.2);
  border-color: rgb(96, 166, 255);
}

.feature-item.hovered .feature-media {
  box-shadow: 0 16px 48px rgba(96, 166, 255, 0.15);
  border-color: rgb(96, 166, 255);
  transform: scale(1.02);
}

.feature-item.playing .feature-media {
  box-shadow: 0 20px 60px rgba(96, 166, 255, 0.3);
  border-color: rgb(96, 166, 255);
  animation: playing-pulse 2s ease-in-out infinite;
}

@keyframes playing-pulse {
  0%, 100% {
    box-shadow: 0 20px 60px rgba(96, 166, 255, 0.3);
  }
  50% {
    box-shadow: 0 20px 60px rgba(96, 166, 255, 0.5);
  }
}

.feature-media .video-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  overflow: visible;
  background: #f8f9fa;
}

.feature-media .video-placeholder {
  color: #6c757d;
  font-size: 1.2rem;
  font-weight: 500;
  text-align: center;
  padding: 20px;
}

/* 统计展示区域 */
.stats-section {
  padding: 40px 0;
  margin-top: 80px;
  background: #fff;
  position: relative;
  overflow: hidden;
}

.stats-section::before {
  content: none;
}

.stats-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.stats-title {
  text-align: center;
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 60px;
  color: #1c1c1c;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3B82F6, #60A5FA, #93C5FD, #DBEAFE);
  background-size: 400% 100%;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.stat-item:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.stat-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  position: relative;
}

.stat-icon svg {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.stat-item:hover .stat-icon svg {
  transform: scale(1.1);
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
}

.stat-content {
  position: relative;
}

.stat-number {
  font-size: 3.5rem;
  font-weight: 800;
  color: #1c1c1c;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-unit {
  font-size: 3.5rem;
  color: #64748b;
  margin-left: 2px;
  vertical-align: baseline;
}

.stat-label {
  font-size: 1.2rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-section {
    padding: 60px 0;
  }
  
  .stats-title {
    font-size: 2.5rem;
    margin-bottom: 40px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .stat-item {
    padding: 30px 20px;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
  
  .stat-label {
    font-size: 1rem;
  }
}

/* 价格方案 */
.pricing-section {
  padding: 60px 0;
  background: white;
}

.pricing-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.pricing-title {
  text-align: center;
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 60px;
  color: #1c1c1c;
}

.pricing-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  margin-top: 40px;
}

.pricing-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 40px;
  position: relative;
  transition: transform 0.3s, box-shadow 0.3s;
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.pricing-card.popular {
  border-color: rgb(96,166,255);
  transform: scale(1.05);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: rgb(96,166,255);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.card-header {
  margin-bottom: 24px;
}

.card-type {
  font-size: 1.3rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-header h3 {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 8px 0 -14px 0;
  color: #1c1c1c;
  line-height: 1.3;
}

.card-header p {
  color: #6c757d;
  line-height: 1.6;
}

.card-price {
  margin-bottom: 24px;
}

.price {
  font-size: 3rem;
  font-weight: 700;
  color: #1c1c1c;
}

.period {
  font-size: 1.3rem;
  color: #6c757d;
}

.card-button {
  width: 100%;
  padding: 16px;
  border: 2px solid rgb(96,166,255);
  background: transparent;
  color: rgb(96,166,255);
  border-radius: 8px;
  font-size: 1.3rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 24px;
  margin-top: auto;
}

.card-button:hover {
  background: rgb(96,166,255);
  color: white;
}

.card-button.primary {
  background: rgb(96,166,255);
  color: white;
}

.card-button.primary:hover {
  background: rgb(76,146,235);
}

.card-features {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
}

.card-features li {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 0;
  color: #1c1c1c;
  font-size: 1.5rem;
  line-height: 1.5;
  text-align: left;
}

.card-features li svg {
  width: 22px;
  height: 22px;
  min-width: 22px;
  min-height: 22px;
  flex-shrink: 0;
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .features-title {
    font-size: 2rem;
    margin-bottom: 60px;
  }
  
  .features-list {
    gap: 80px;
  }
  
  .feature-item {
    grid-template-columns: 1fr;
    gap: 40px;
    direction: ltr !important;
  }
  
  .feature-item:nth-child(even) {
    direction: ltr;
  }
  
  .feature-content h3 {
    font-size: 2rem;
  }
  
  .feature-media {
    height: 300px;
  }

  .pricing-cards {
    grid-template-columns: 1fr;
  }

  .pricing-card.popular {
    transform: none;
  }
}

/* 售前咨询悬浮组件 */
.presales-consultation {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 999;
}

.consultation-trigger {
  background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%);
  color: white;
  padding: 20px 16px;
  border-radius: 30px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  min-height: 120px;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.6), 0 0 0 8px rgba(59, 130, 246, 0.1);
  }
  100% {
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  }
}

.consultation-trigger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563EB 0%, #3B82F6 100%);
  animation: none;
}

.consultation-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.consultation-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
}

.consultation-text span {
  display: block;
  text-align: center;
}

.consultation-popup {
  position: absolute;
  right: 80px;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  width: 280px;
  max-height: 450px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.popup-header {
  background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid #E2E8F0;
}

.popup-header h3 {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1E293B;
}

.popup-header p {
  margin: 2px 0;
  font-size: 12px;
  color: #64748B;
  line-height: 1.3;
}

.popup-content {
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
}

.contact-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  object-fit: contain;
  max-height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .presales-consultation {
    right: 20px;
  }

  .consultation-popup {
    right: 70px;
    width: 260px;
    max-width: calc(100vw - 90px);
    max-height: 400px;
  }

  .consultation-trigger {
    padding: 14px 10px;
    font-size: 13px;
    min-height: 85px;
  }

  .consultation-text {
    font-size: 14px;
    font-weight: 700;
  }

  .popup-header {
    padding: 10px 12px;
  }

  .popup-header h3 {
    font-size: 14px;
  }

  .popup-header p {
    font-size: 11px;
  }

  .contact-image {
    max-height: 250px;
  }

  .feature-title {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .consultation-popup {
    right: 60px;
    width: 240px;
    max-width: calc(100vw - 80px);
    max-height: 350px;
  }

  .popup-header {
    padding: 8px 10px;
  }

  .popup-header h3 {
    font-size: 13px;
  }

  .popup-header p {
    font-size: 10px;
  }

  .contact-image {
    max-height: 200px;
  }

  .consultation-trigger {
    padding: 12px 8px;
    font-size: 12px;
    min-height: 75px;
  }

  .consultation-text {
    font-size: 13px;
    font-weight: 700;
  }

  .consultation-icon svg {
    width: 32px;
    height: 32px;
  }
}

.download-btn {
  background: rgb(96,166,255);
  color: #fff;
  border: none;
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 1.05rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
  text-decoration: none;
  box-shadow: 0 2px 8px rgba(96,166,255,0.08);
  margin-top: 20px;
  display: inline-block;
  white-space: nowrap;
  min-width: 180px;
}
.download-btn:hover {
  background: rgb(76,146,235);
  color: #fff;
  text-decoration: none;
}

.download-buttons-group {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.download-buttons-group .download-btn {
  margin-top: 0;
  width: fit-content;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modal-fade-in 0.3s ease;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1c1c1c;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background-color: #f8f9fa;
}

.modal-body {
  padding: 24px;
}

.download-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.download-option {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.download-option:hover {
  background-color: #f8f9fa;
}

.download-option input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: rgb(96,166,255);
}

.option-text {
  font-size: 1rem;
  color: #1c1c1c;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0 24px 24px 24px;
}

.modal-footer .btn-primary,
.modal-footer .btn-secondary {
  padding: 12px 24px;
  font-size: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.modal-footer .btn-secondary {
  background: #6c757d;
  color: white;
}

.modal-footer .btn-secondary:hover {
  background: #5a6268;
}

.mistake-carousel {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
}

.mistake-carousel .el-carousel__item {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 16px;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 16px;
}

/* 需要改变vue自带的样式时需要在元素前面加上::v-deep*/
/* 左箭头 */
:deep(.el-carousel__arrow--left) {
  top: 50%;
  left: 10px;
  font-size: 24px;
  font-weight: 900;
  color: rgb(96, 166, 255);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 右箭头 */
:deep(.el-carousel__arrow--right) {
  top: 50%;
  right: 10px;
  font-size: 24px;
  color: rgb(96, 166, 255);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 水平指示器 */
:deep(.el-carousel__indicators--horizontal) {
  bottom: 20px;
  border-radius: 20px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 将轮播图指示器变成圆点 */
:deep(.el-carousel__indicator--horizontal .el-carousel__button) {
  width: 8px;
  height: 8px;
  background: rgba(108, 117, 125, 0.3);
  border: none;
  border-radius: 50%;
  opacity: 0.7;
  transition: all 0.3s ease;
}

/* 当前被选中的指示器样式 */
:deep(.el-carousel__indicator--horizontal.is-active .el-carousel__button) {
  width: 8px;
  height: 8px;
  background: rgb(96, 166, 255);
  border-radius: 50%;
  opacity: 1;
  transform: scale(1.2);
  box-shadow: 0 1px 4px rgba(96, 166, 255, 0.4);
}

:deep(.el-carousel__container) {
  width: 100%;
  height: 100%;
}
</style> 