<template>
  <div class="education-page">
    <!-- 顶部英雄区 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-inner">
          <div class="hero-text">
            <h1 class="hero-title">AI 重塑作业批改</h1>
            <h2 class="hero-subtitle">让教师减负，让教学增效</h2>
            <div class="hero-spacer"></div>
            <p class="hero-description">
              喵喵汪汪智能作业批改系统，通过 AI 技术实现全学科作业自动批改、错题智能归集与学情数据可视化，帮助教师节省 80% 批改时间，让教学更精准、学生进步更明显。
            </p>
            <div class="hero-spacer"></div>
            <div class="hero-buttons">
              <!-- <button class="btn-primary" @click="goToLogin">立即体验</button> -->
            </div>
          </div>
          <div class="hero-image">
            <img src="/images/miaomiaowangwang.png" alt="AI教育助手" class="hero-img">
          </div>
        </div>
      </div>
    </section>

    <!-- 四大卖点 -->
    <section class="features-section">
      <div class="features-container">
        <div class="features-list">
          <div class="feature-item">
            <div class="placeholder-img" aria-label="极速批改">图片</div>
            <h3>极速批改</h3>
            <p>25页/分钟高速处理，客观题秒判，主观题智能辅助，告别熬夜批作业。</p>
          </div>
          <div class="feature-item">
            <div class="placeholder-img" aria-label="精准识别">图片</div>
            <h3>精准识别</h3>
            <p>手写文字、公式图形全识别，对错题判断准确率超98%，比人工更稳定。</p>
          </div>
          <div class="feature-item">
            <div class="placeholder-img" aria-label="自动错题本">图片</div>
            <h3>自动错题本</h3>
            <p>按知识点归类错题，关联薄弱环节，学生针对性复习，效率翻倍。</p>
          </div>
          <div class="feature-item">
            <div class="placeholder-img" aria-label="数据化教学">图片</div>
            <h3>数据化教学</h3>
            <p>班级/年级成绩图表化展示，知识点掌握情况一目了然，教学更有方向。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 学术诚信与AI透明度tab区块 -->
    <section class="tabs-section">
      <div class="tabs-container">
        <div class="tabs-header">
          <button 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="['tab-button', { active: activeTab === index }]"
            @click="activeTab = index"
          >
            {{ tab.title }}
          </button>
        </div>
        <div class="tab-content">
          <div v-for="(tab, index) in tabs" :key="index" v-show="activeTab === index" class="tab-panel">
            <div class="tab-panel-inner">
              <div class="tab-panel-text">
                <h3>{{ tab.feature.title }}</h3>
                <p>{{ tab.feature.description }}</p>
              </div>
              <div class="tab-panel-media">
                <div class="placeholder-img" :aria-label="tab.feature.title">图片</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- AI赋能教学展示区 -->
    <section class="ai-section">
      <div class="ai-container">
        <h2 class="ai-title">AI 让批改更智能</h2>
        <div class="ai-features">
          <div class="ai-feature-card">
            <div class="placeholder-img" aria-label="主客观题全批">图片</div>
            <h4>主客观题全批</h4>
            <p>客观题自动判分，主观题提取关键词辅助批改，作文还能分析结构与语言规范。</p>
          </div>
          <div class="ai-feature-card">
            <div class="placeholder-img" aria-label="错题自动归集">图片</div>
            <h4>错题自动归集</h4>
            <p>按学生、班级、知识点分类错题，生成专属错题本，附带强化练习推荐。</p>
          </div>
          <div class="ai-feature-card">
            <div class="placeholder-img" aria-label="电子试卷管理">图片</div>
            <h4>电子试卷管理</h4>
            <p>扫描试卷自动存档，支持按班级、时间、学生姓名快速检索，随时对比分析。</p>
          </div>
          <div class="ai-feature-card">
            <div class="placeholder-img" aria-label="灵活硬件方案">图片</div>
            <h4>灵活硬件方案</h4>
            <p>支持打印机买断或租赁，包含耗材维护套餐，满足不同学校预算需求。</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Education',
  data() {
    return {
      activeTab: 0,
      tabs: [
        {
          title: '全学科覆盖',
          feature: {
            title: '小初高全学科适配',
            description: '支持语文、数学、英语等多学科作业批改，涵盖选择、判断、作文、应用题等全题型，满足不同学段教学需求。',
            image: '/Free AI Writing Assistance for Students _ Grammarly_files/left__3_.png'
          }
        },
        {
          title: '智能识别技术',
          feature: {
            title: '复杂内容精准识别',
            description: '不仅能识别工整手写，连涂改、褶皱试卷、偏离答题区的内容也能准确判断，还支持数学公式、化学方程式等特殊符号识别。',
            image: '/Free AI Writing Assistance for Students _ Grammarly_files/left__4_.png'
          }
        },
        {
          title: '学情分析',
          feature: {
            title: '用数据驱动教学',
            description: '自动生成平均分、得分率、知识点掌握热力图，跟踪学生成绩趋势，让教师清晰了解班级薄弱点，针对性辅导。',
            image: '/Free AI Writing Assistance for Students _ Grammarly_files/UI__Plagiariam_AI_Detector_Pro.png'
          }
        },
        {
          title: '灵活适配',
          feature: {
            title: '贴合真实教学场景',
            description: '无需专用答题卡，支持练习册、试卷、作业本等多种形式，兼容8K、16K等常见纸张，即学即用无需改变教学习惯。',
            image: '/Free AI Writing Assistance for Students _ Grammarly_files/Frame_73.png'
          }
        },
        {
          title: '饱受好评',
          feature: {
            title: '试用好评如潮',
            description: '众多试用学校和一线教师纷纷表示，自动作业批改机极大提升了作业批改的准确率和速度。节省了大量时间，能将更多精力投入到教学和学生辅导中，对提升教学质量帮助显著。',
            image: ''
          }
        }
      ]
    }
  },
  methods: {
    goToLogin() {
      window.open('https://saomiaoshijuan.com/login', '_blank');
    }
  }
}
</script>

<style scoped>
.education-page {
  min-height: 100vh;
  background: #f8f9fa;
  overflow-x: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}
.hero-section {
  background: linear-gradient(90deg, rgba(96,166,255,0.15) 0%, #fff 100%);
  padding: 48px 0 32px 0;
}
.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.hero-inner {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.hero-text {
  flex: 1 1 400px;
  min-width: 320px;
  padding: 24px;
}
.hero-title {
  font-size: 2.8em;
  font-weight: bold;
  margin-bottom: 0.5em;
  color: rgb(96,166,255);
}
.hero-subtitle {
  font-size: 1.5em;
  color: rgb(96,166,255);
  margin-bottom: 1em;
}
.hero-description {
  font-size: 1.4em;
  color: #444;
  margin-bottom: 1.5em;
  text-align: left;
}
.hero-buttons {
  margin-top: 1.5em;
}
.btn-primary {
  background: rgb(96,166,255);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.8em 2em;
  font-size: 1.1em;
  cursor: pointer;
  transition: background 0.2s;
}
.btn-primary:hover {
  background: rgba(96,166,255,0.85);
}
.hero-image {
  flex: 1 1 400px;
  min-width: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hero-img {
  max-width: 350px;
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(96,166,255,0.08);
}
.features-section {
  background: #fff;
  padding: 40px 0 24px 0;
}
.features-container {
  max-width: 1100px;
  margin: 0 auto;
}
.features-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 32px;
}
.feature-item {
  flex: 1 1 200px;
  min-width: 200px;
  background: rgba(96,166,255,0.15);
  border-radius: 12px;
  padding: 32px 20px 24px 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(96,166,255,0.04);
}
.placeholder-img {
  width: 64px;
  height: 64px;
  background: #e3eaf6;
  color: #2563eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1em;
  font-weight: bold;
  border-radius: 12px;
  margin: 0 auto 12px auto;
  box-shadow: 0 2px 8px 0 rgba(96,166,255,0.08);
}
.tabs-section {
  background: #f8f9fa;
  padding: 48px 0 32px 0;
}
.tabs-container {
  max-width: 1100px;
  margin: 0 auto;
}
.tabs-header {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 32px;
}
.tab-button {
  background: #fff;
  border: 1px solid rgb(96,166,255);
  color: rgb(96,166,255);
  border-radius: 8px 8px 0 0;
  padding: 12px 32px;
  font-size: 1.1em;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.tab-button.active, .tab-button:hover {
  background: rgb(96,166,255);
  color: #fff;
}
.tab-content {
  background: #fff;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 8px rgba(96,166,255,0.04);
  padding: 32px 24px;
  min-height: 160px;
}
.tab-panel-inner {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 32px;
}
.tab-panel-text {
  flex: 1 1 320px;
  min-width: 260px;
}
.tab-panel-media {
  flex: 1 1 320px;
  min-width: 260px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tab-panel-text p {
  text-align: left;
  font-size: 1.1em;
}
.ai-section {
  background: #fff;
  padding: 48px 0 48px 0;
}
.ai-container {
  max-width: 1100px;
  margin: 0 auto;
}
.ai-title {
  text-align: center;
  font-size: 2em;
  color: rgb(96,166,255);
  margin-bottom: 32px;
}
.ai-features {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: space-between;
}
.ai-feature-card {
  flex: 1 1 220px;
  min-width: 220px;
  background: rgba(96,166,255,0.15);
  border-radius: 12px;
  padding: 32px 20px 24px 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(96,166,255,0.04);
}
.ai-feature-card:nth-child(-n+3) p {
  text-align: left;
}
.ai-feature-card:last-child p {
  text-align: center;
}
.feature-item p {
  text-align: left;
  font-size: 1.1em;
}
@media (max-width: 900px) {
  .hero-inner, .features-list, .ai-features, .tab-panel-inner {
    flex-direction: column;
    align-items: stretch;
  }
  .hero-image, .hero-text, .tab-panel-text, .tab-panel-media {
    min-width: 0;
  }
}
</style> 