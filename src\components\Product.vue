<template>
  <div class="product-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-inner">
          <div class="hero-text">
            <h1 class="hero-title">AI 智能作业批改系统</h1>
            <h2 class="hero-subtitle">让作业批改更高效、更精准的智能解决方案</h2>
            <div class="hero-spacer"></div>
            <p class="hero-description">
              自动作业批改机是专为解决教师作业批改难题设计的 AI 智能助手，支持留痕批改、错题归集与学情分析，大幅节省时间成本，提升教学效率。
            </p>
            <div class="hero-spacer"></div>
            <div class="hero-buttons">
              <!-- <button class="btn-primary" @click="goToLogin">立即体验</button> -->
            </div>
          </div>
          <div class="hero-image">
            <video src="/videos/硬件集成演示.mp4" class="hero-img" autoplay loop muted playsinline controls style="max-width: 60%; border-radius: 12px;">
              您的浏览器不支持视频播放。
            </video>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="features-container">
        <div class="features-header">
          <h2 class="features-title">重新定义作业批改流程</h2>
          <div class="features-spacer"></div>
          <p class="features-description">
            通过 AI 技术实现客观题自动批改、主观题辅助审阅，结合智能错题本与学情分析，让教学更具针对性，助力学生高效提分。
          </p>
          <div class="features-spacer"></div>
        </div>

        <!-- Tabs Section -->
        <div class="tabs-container">
          <div class="tabs-header">
            <button 
              v-for="(tab, index) in tabs" 
              :key="index"
              :class="['tab-button', { active: activeTab === index }]"
              @click="activeTab = index"
            >
              {{ tab.title }}
            </button>
          </div>

          <div class="tab-content">
            <div 
              v-for="(tab, index) in tabs" 
              :key="index"
              :class="['tab-panel', { active: activeTab === index }]"
            >
              <div class="feature-showcase">
                <div class="feature-text">
                  <h3 class="feature-title">{{ tab.feature.title }}</h3>
                  <div class="feature-spacer"></div>
                  <p class="feature-description">{{ tab.feature.description }}</p>
                  <div class="feature-spacer-small"></div>
                </div>
                <div class="feature-media">
                  <div class="media-container">
                    <img :src="tab.feature.image" :alt="tab.title" class="feature-img" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- AI Features Section -->
    <section class="ai-features-section">
      <div class="ai-features-container">
        <div class="ai-features-header">
          <h2 class="ai-features-title">AI 赋能的教学辅助工具</h2>
          <div class="ai-features-spacer"></div>
          <p class="ai-features-description">
            深度融合 AI 技术与教学场景，通过智能识别、数据分析与自动化处理，为教师减负，为教学增效。
          </p>
          <div class="ai-features-spacer"></div>
        </div>

        <div class="ai-features-grid">
          <div class="ai-feature-card">
            <div class="ai-feature-content">
              <h3 class="ai-feature-title">全场景作业适配</h3>
              <p class="ai-feature-description">
                无需专用答题卡，支持手写试卷、打印试卷等多种形式，兼容涂改液、便利贴等特殊情况，真实还原人工批改场景。
              </p>
            </div>
            <div class="ai-feature-media">
              <el-carousel 
                :interval="4000" 
                height="400px"
                indicator-position="inside"
                :autoplay="true"
                class="product-carousel"
              >
                <el-carousel-item v-for="(img, idx) in carouselImages" :key="idx">
                  <img 
                    :src="img" 
                    alt="全场景作业适配轮播" 
                    class="carousel-image"
                  />
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>

          <!-- 作文智能批改部分已注释
          <div class="ai-feature-card reverse">
            <div class="ai-feature-content">
              <h3 class="ai-feature-title">作文智能批改</h3>
              <p class="ai-feature-description">
                语文作文自动检测内容完整性、结构合理性与语言规范性；英语作文智能检查语法错误、拼写问题，提供表达优化建议。
              </p>
            </div>
            <div class="ai-feature-media">
              <div class="placeholder-img" aria-label="图片">图片</div>
            </div>
          </div>
          -->

          <div class="ai-feature-card">
            <div class="ai-feature-content">
              <h3 class="ai-feature-title">数据驱动学情分析</h3>
              <p class="ai-feature-description">
                自动统计班级平均分、各题得分率，生成知识点掌握雷达图与成绩趋势曲线，让教师直观掌握学情，精准制定教学计划。
              </p>
            </div>
            <div class="ai-feature-media">
              <el-carousel 
                :interval="4000" 
                height="400px"
                indicator-position="inside"
                :autoplay="true"
                class="product-carousel"
              >
                <el-carousel-item v-for="(img, idx) in analysisImages" :key="idx">
                  <img 
                    :src="img" 
                    alt="数据驱动学情分析轮播" 
                    class="carousel-image"
                  />
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Product',
  data() {
    return {
      activeTab: 0,
      tabs: [
        {
          title: '快速批改',
          feature: {
            title: '秒级响应的全批全改',
            description: '25页/分钟高速处理，支持A3/A4/8K/16K多种纸张，客观题自动判分，主观题智能标注，效率提升90%以上。',
            image: '/images/快速批改.png'
          }
        },
        {
          title: '精准识别',
          feature: {
            title: '超越人工的识别能力',
            description: '精准识别手写文字、数学公式、化学方程式及图形，支持涂改、褶皱等特殊试卷处理，准确率达98%以上。',
            image: '/images/精准识别.png'
          }
        },
        {
          title: '错题管理',
          feature: {
            title: '自动生成个性化错题本',
            description: '按知识点智能归类错题，关联薄弱环节推荐强化练习，帮助学生针对性突破，教师可一键查看班级高频错题。',
            image: '/images/错题管理.png'
          }
        },
        {
          title: '学情分析',
          feature: {
            title: '多维度数据可视化报告',
            description: '自动生成班级/年级成绩分布、知识点掌握热力图，追踪学生成绩趋势，为教学策略提供数据支撑。',
            image: '/images/学情分析.png'
          }
        },
        {
          title: '全学科覆盖',
          feature: {
            title: '适配小初高全学科需求',
            description: '支持语文、数学、英语等多学科作业批改，涵盖客观题、主观题、作文等题型，满足不同学段教学场景。',
            image: '/images/全学科覆盖.png'
          }
        }
      ],
      carouselImages: [
        '/images/全场景作业适配1.png',
        '/images/全场景作业适配2.png',
        '/images/全场景作业适配3.png'
      ],
      analysisImages: [
        '/images/数据驱动学情分析1.png',
        '/images/数据驱动学情分析2.png'
      ]
    }
  },
  methods: {
    goToLogin() {
      window.open('https://saomiaoshijuan.com/login', '_blank');
    }
  },

}
</script>

<style scoped>
.product-page {
  min-height: 100vh;
  background: #f8f9fa;
  overflow-x: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Hero Section */
.hero-section {
  color: inherit;
  padding: 80px 0;
  width: 100%;
  margin: 0;
  position: relative;
  left: 0;
  right: 0;
  margin-top: 0;
  padding-top: 0;
  box-sizing: border-box;
  border: none;
  background: none;
}

.hero-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.hero-inner {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 2.8rem;
  font-weight: 500;
  margin-bottom: 40px;
  line-height: 1.2;
  text-align: left;
}

.hero-spacer {
  height: 40px;
}

.hero-description {
  font-size: 1.6rem;
  line-height: 1.6;
  margin-bottom: 40px;
  opacity: 0.9;
  text-align: left;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.btn-primary {
  background: rgb(96,166,255);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary:hover {
  background: rgb(76,146,235);
}

.cta-description {
  font-size: 0.9rem;
  opacity: 0.8;
}

.link {
  color: #4CAF50;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-img {
  max-width: 60%;
  height: auto;
  border-radius: 12px;
}

/* Features Section */
.features-section {
  padding: 20px 0;
  background: white;
  width: 100%;
}

.features-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.features-header {
  text-align: center;
  margin-bottom: 16px;
}

.features-title {
  font-size: 3rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 40px;
}

.features-spacer {
  height: 40px;
}

.features-description {
  font-size: 1.4rem;
  line-height: 1.6;
  color: #666;
  max-width: 1200px;
  margin: 0 auto;
  white-space: nowrap;
}

/* Tabs */
.tabs-container {
  margin-top: 60px;
}

.tabs-header {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 60px;
  flex-wrap: wrap;
}

.tab-button {
  padding: 20px 32px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 1.3rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  color: #1a1a1a;
  background: #f8f9fa;
  border-bottom-color: rgb(96,166,255, 0.3);
}

.tab-button.active {
  color: rgb(96,166,255);
  border-bottom-color: rgb(96,166,255);
  background: #e0eaff;
}

.tab-content {
  position: relative;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
}

.feature-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: center;
}

.feature-text {
  padding-right: 40px;
}

.feature-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.feature-spacer {
  height: 16px;
}

.feature-spacer-small {
  height: 8px;
}

.feature-description {
  font-size: 1.37rem;
  line-height: 1.6;
  color: #666;
  text-align: left;
}

.feature-media {
  display: flex;
  justify-content: center;
  align-items: center;
}

.media-container {
  width: 500;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px auto;
}

.feature-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

/* AI Features Section */
.ai-features-section {
  padding: 80px 0;
  background: #f8f9fa;
  width: 100%;
}

.ai-features-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.ai-features-header {
  text-align: center;
  margin-bottom: 60px;
}

.ai-features-title {
  font-size: 3rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 40px;
}

.ai-features-spacer {
  height: 40px;
}

.ai-features-description {
  font-size: 1.7rem;
  line-height: 1.6;
  color: #666;
  max-width: 1200px;
  margin: 0 auto;
  white-space: nowrap;
}

.ai-features-grid {
  display: flex;
  flex-direction: column;
  gap: 80px;
}

.ai-feature-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.ai-feature-card.reverse {
  direction: rtl;
}

.ai-feature-card.reverse .ai-feature-content {
  direction: ltr;
}

.ai-feature-content {
  padding: 0 40px;
}

.ai-feature-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.ai-feature-description {
  font-size: 1.4rem;
  line-height: 1.6;
  color: #666;
  text-align: left;
}

.ai-feature-media {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-feature-img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-inner {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .hero-title {
    text-align: center;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .hero-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 20px;
  }

  .feature-showcase {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .feature-text {
    padding-right: 0;
  }

  .ai-feature-card {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .ai-feature-card.reverse {
    direction: ltr;
  }

  .ai-feature-content {
    padding: 0;
  }

  .tabs-header {
    flex-direction: column;
    align-items: center;
  }

  .tab-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .hero-section,
  .features-section,
  .ai-features-section {
    padding: 40px 0;
  }

  .hero-container,
  .features-container,
  .ai-features-container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .features-title,
  .ai-features-title {
    font-size: 1.75rem;
  }
}

.placeholder-img {
  width: 64px;
  height: 64px;
  background: #e3eaf6;
  color: #2563eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1em;
  font-weight: bold;
  border-radius: 12px;
  margin: 0 auto 12px auto;
  box-shadow: 0 2px 8px 0 rgba(96,166,255,0.08);
}

.product-carousel {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
}

.product-carousel .el-carousel__item {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 16px;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 16px;
}

/* 需要改变vue自带的样式时需要在元素前面加上::v-deep*/
/* 左箭头 */
:deep(.el-carousel__arrow--left) {
  top: 50%;
  left: 10px;
  font-size: 24px;
  font-weight: 900;
  color: rgb(96, 166, 255);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 右箭头 */
:deep(.el-carousel__arrow--right) {
  top: 50%;
  right: 10px;
  font-size: 24px;
  color: rgb(96, 166, 255);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 水平指示器 */
:deep(.el-carousel__indicators--horizontal) {
  bottom: 20px;
  border-radius: 20px;
  padding: 8px 12px;
  background: transparent;
}

/* 将轮播图指示器变成圆点 */
:deep(.el-carousel__indicator--horizontal .el-carousel__button) {
  width: 8px;
  height: 8px;
  background: #cccccc;
  border: none;
  border-radius: 50%;
  opacity: 1;
  transition: all 0.3s ease;
}

/* 当前被选中的指示器样式 */
:deep(.el-carousel__indicator--horizontal.is-active .el-carousel__button) {
  width: 8px;
  height: 8px;
  background: rgb(96, 166, 255);
  border-radius: 50%;
  opacity: 1;
  transform: scale(1.2);
  box-shadow: 0 1px 4px rgba(96, 166, 255, 0.6);
}

:deep(.el-carousel__container) {
  width: 100%;
  height: 100%;
}
</style> 