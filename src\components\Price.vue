<template>
  <!-- 报价表格展示，提前到前面 -->
  <div class="excel-table-container">
    <h3 class="excel-table-title center-title">喵喵汪汪自助作业批改系统清单报价</h3>
    <!-- 联系人信息块 -->
    <div class="contact-info-block">
      <div><b>联系人：</b>段云飞</div>
      <div><b>联系电话：</b>13666680278</div>
      <div><b>邮箱：</b><EMAIL></div>
      <div><b>报价时间：</b>{{ currentDate }}</div>
    </div>
    <el-table
      v-if="excelRows.length > 0"
      :data="excelRows"
      border
      style="width: 100%; margin-top: 24px;"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
    >
      <el-table-column prop="col0" label="序号" :min-width="getColWidth(0)" />
      <el-table-column prop="col1" label="类别" :min-width="getColWidth(1)" />
      <el-table-column prop="col2" label="服务名称" :min-width="getColWidth(2)" />
      <el-table-column prop="col3" label="参数" :min-width="getColWidth(3)" />
      <el-table-column prop="col4" label="单位" :min-width="getColWidth(4)" />
      <el-table-column label="数量" :min-width="getColWidth(5)">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <template v-if="scope.row.col0 !== '' && scope.row.col5 !== '按需'">
              <el-input
                v-model="scope.row.col5"
                type="number"
                :min="0"
                size="small"
                @change="handleQuantityChange(scope.row)"
                style="width: 80px;"
                placeholder="数量"
              />
            </template>
            <template v-else>
              {{ scope.row.col5 }}
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="col6" label="单价" :min-width="getColWidth(6)" />
      <el-table-column prop="col7" label="金额（元）" :min-width="getColWidth(7)" />
      <el-table-column
        label="选择"
        width="80"
        align="center"
      >
        <template #default="scope">
          <el-radio
            v-if="scope.row.col0 === 1 || scope.row.col0 === 2"
            v-model="hardwareSelection"
            :label="scope.row.col0"
            @change="handleHardwareSelection"
            style="margin-right: 0;"
          ></el-radio>
          <el-checkbox
            v-else-if="scope.row.col0 === 3 || scope.row.col0 === 4"
            v-model="scope.row.required"
            @change="handleRequiredChange"
          ></el-checkbox>
          <el-radio
            v-else-if="scope.row.col0 === 5 || scope.row.col0 === 6"
            v-model="consumableSelection"
            :label="scope.row.col0"
            @change="handleConsumableSelection"
            style="margin-right: 0;"
          ></el-radio>
        </template>
      </el-table-column>
    </el-table>
    <!-- 备注信息块 -->
    <div class="remark-block">
      <div><b>备注</b></div>
      <div>1. 结算方式及期限：甲乙方合同签订后甲方在三个工作日之内完成该订单全款支付。</div>
      <div>2. 交货期限：可协商。</div>
      <div>3. 违约责任：按合同法执行。</div>
      <div>4. 本报价自双方签字盖章起生效。（传真件有效）</div>
      <div>5. 其他约定事项：未尽事宜，双方友好协商解决。</div>
    </div>
  </div>

  <!-- 价格方案区域，后置 -->
  <section class="pricing-section">
    <div class="pricing-container">
      <h2 class="pricing-title">选择适合您的方案</h2>
      <div class="pricing-cards">
        <div class="pricing-card">
          <div class="card-header">
            <span class="card-type">基础版</span>
            <h3>小型学校</h3>
          </div>
          <div class="card-price">
            <span class="price">¥15,000</span>
            <span class="period">一次性支付</span>
          </div>
          <button class="card-button" @click="showContactModal = true">立即咨询</button>
          <ul class="card-features">
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 技术服务</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 15000 张 A3 作业正反批改算力</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 软件技术支持服务</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 单个学校首次使用支持</li>
          </ul>
        </div>

        <div class="pricing-card popular">
          <div class="popular-badge">推荐</div>
          <div class="card-header">
            <span class="card-type">标准版</span>
            <h3>中等规模学校</h3>
          </div>
          <div class="card-price">
            <span class="price">¥66,000</span>
            <span class="period">一次性支付</span>
          </div>
          <button class="card-button primary" @click="showContactModal = true">立即咨询</button>
          <ul class="card-features">
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> <b>打印机买断（理光 IMC 2510 + 高扫 + 定制模块）</b></li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 技术服务</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 软件技术支持服务</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 打印机耗材维护套餐（含15000 张 A3/8k 作业双面彩色打印耗材）</li>
          </ul>
        </div>

        <div class="pricing-card">
          <div class="card-header">
            <span class="card-type">租赁版</span>
            <h3>支持按年付费</h3>
          </div>
          <div class="card-price">
            <span class="price">¥30,000</span>
            <span class="period">每年支付</span>
          </div>
          <button class="card-button" @click="showContactModal = true">立即咨询</button>
          <ul class="card-features">
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 打印机租赁服务（理光 IMC 2510 + 高扫 + 定制模块）</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 3 年后资产归甲方</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 技术服务</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 喵喵汪汪作业批改软件 V1.0 软件技术支持服务</li>
            <li><svg width="22" height="22" viewBox="0 0 22 22" fill="none"><circle cx="11" cy="11" r="10" stroke="#1c1c1c" stroke-width="2"/><path d="M6 12l3 3 7-7" stroke="#1c1c1c" stroke-width="2" fill="none" stroke-linecap="round"/></svg> 打印机耗材维护套餐</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- 联系弹窗 -->
  <ContactModal :visible="showContactModal" @close="showContactModal = false" />
</template>

<script>
import ContactModal from './ContactModal.vue'

export default {
  name: 'Price',
  components: {
    ContactModal
  },
  data() {
    // 写死表头
    const excelHeader = [
      '序号', '类别', '服务名称', '参数', '单位', '数量', '单价', '金额（元）'
    ];
    // 写死表格数据
    const excelRows = [
      { col0: 1, col1: '硬件', col2: '打印机买断', col3: '理光 IMC 2510+高扫+定制模块', col4: '套', col5: 1, col6: 36000, col7: 36000 },
      { col0: 2, col1: '硬件租赁', col2: '打印机租赁服务', col3: '租赁服务，理光 IMC 2510+高扫，3年合同后资产归甲方', col4: '年', col5: 0, col6: 15000, col7: 0 },
      { col0: 3, col1: '技术服务', col2: '喵喵汪汪作业批改软件 V1.0技术服务', col3: '15000张A3作业正反批改算力（1张A3=2面A3=4面A4，1面A3=1面8K,1面A4=1面16K）\n具体功能规格见附件', col4: '套', col5: 1, col6: 15000, col7: 15000, required: true },
      { col0: 4, col1: '技术服务', col2: '喵喵汪汪作业批改软件 V1.0软件技术支持服务', col3: '含单个学校的首次使用技术支持服务', col4: '学校', col5: 0, col6: 5000, col7: 0, required: true },
      { col0: 5, col1: '耗材服务', col2: '打印机耗材维护套餐', col3: '15000张A3作业正反彩色打印', col4: '套', col5: 1, col6: 15000, col7: 15000 },
      { col0: 6, col1: '耗材服务', col2: '打印机耗材和维护自行购买', col3: '打印机耗材和维护自行购买', col4: '套', col5: '按需', col6: '', col7: '' },
      { col0: '', col1: '', col2: '打印机耗材维护套餐全包服务', col3: '1张A3=2面A3=4面A4，1面A3=1面8K,1面A4=1面16K\n黑白打印A4:0.025元，彩色打印A4:0.25元\n购买了耗材维护套餐，每个月至少保养一次，黑白彩色10:1转换\n包含所有非人为损坏的维修维护服务', col4: '', col5: '', col6: '', col7: '' },
      { col0: '', col1: '', col2: '', col3: '', col4: '合计', col5: '', col6: '', col7: 66000 }
    ];
    // 获取当前日期，格式为YYYY年MM月DD日
    const now = new Date();
    const currentDate = `${now.getFullYear()}年${String(now.getMonth()+1).padStart(2,'0')}月${String(now.getDate()).padStart(2,'0')}日`;
    return {
      showContactModal: false,
      excelHeader,
      excelRows,
      currentDate,
      hardwareSelection: 1, // 硬件选择：1=买断，2=租赁
      consumableSelection: 5, // 耗材选择：5=套餐，6=自行购买
    }
  },
  mounted() {
    // 初始化时计算所有行的金额
    this.initializeAmounts();
    // 初始化时计算合计
    this.calculateTotal();
  },
  methods: {
    // 表头样式
    headerCellStyle() {
      return {
        background: '#eaf3fb',
        color: '#1c1c1c',
        fontWeight: 'bold',
        fontSize: '16px',
        border: '1px solid #bcdffb',
        textAlign: 'left'
      }
    },
    // 单元格样式
    cellStyle({ row, column, rowIndex, columnIndex }) {
      // 可根据不同列设置不同颜色
      const colors = ['#f7fbff', '#fef6f0', '#f7f9fa', '#f7fbff', '#fef6f0', '#f7f9fa', '#f7fbff', '#fef6f0', '#f7f9fa', '#f7fbff', '#fef6f0', '#f7f9fa', '#f7fbff'];
      let style = {
        background: colors[columnIndex % colors.length],
        border: '1px solid #e0e6ed',
        textAlign: 'left',
        fontSize: '15px'
      };
      // 参数列（第4列）支持换行
      if (columnIndex === 3) {
        style.whiteSpace = 'pre-line';
        style.lineHeight = '1.6';
        style.padding = '12px 8px';
      }
      // 合计行加粗加大字号
      if (row.col4 === '合计') {
        style.fontWeight = 'bold';
        style.fontSize = '18px';
      }
      return style;
    },
    getColWidth(idx) {
      // 可根据内容自适应宽度
      const widths = [60, 80, 180, 260, 60, 60, 80, 100, 60, 100, 100, 100, 100];
      return widths[idx] ? widths[idx] + 'px' : '100px';
    },
    
    // 初始化所有行的金额
    initializeAmounts() {
      this.excelRows.forEach((row, index) => {
        // 跳过最后一行（合计行）和第六行（按需行）
        if (index === this.excelRows.length - 1 || index === 5) return;
        
        // 计算金额 = 数量 × 单价
        if (row.col5 !== undefined && row.col5 !== '' && row.col6 !== undefined && row.col6 !== '') {
          row.col7 = row.col5 * row.col6;
        }
      });
    },
    // 处理硬件选择变化
    handleHardwareSelection(value) {
      console.log('硬件选择:', value);
      this.calculateTotal();
    },
    // 处理必选项变化
    handleRequiredChange() {
      console.log('必选项变化');
      // 如果用户尝试取消选择，强制保持选中状态并提示
      this.$nextTick(() => {
        this.excelRows[2].required = true; // 第3行
        this.excelRows[3].required = true; // 第4行
        this.$message({
          message: '此项必选',
          type: 'warning',
          duration: 2000
        });
        this.calculateTotal();
      });
    },
    // 处理耗材选择变化
    handleConsumableSelection(value) {
      console.log('耗材选择:', value);
      this.calculateTotal();
    },
    // 处理数量变化
    handleQuantityChange(row) {
      console.log('数量变化:', row.col5, row);
      
      // 如果是"按需"，不参与计算
      if (row.col5 === '按需') {
        row.col7 = '';
        this.calculateTotal();
        return;
      }
      
      // 确保数量为非负数
      if (row.col5 < 0) {
        row.col5 = 0;
      }
      
      // 重新计算该行的金额
      if (row.col6 && row.col5 !== '') {
        row.col7 = row.col6 * row.col5;
      } else {
        row.col7 = 0;
      }
      
      console.log('计算后金额:', row.col7);
      
      // 重新计算总价
      this.calculateTotal();
    },
    
    // 计算合计金额
    calculateTotal() {
      let total = 0;
      
      // 遍历所有行，计算选中项的金额
      this.excelRows.forEach((row, index) => {
        // 跳过最后一行（合计行）
        if (index === this.excelRows.length - 1) return;
        
        let shouldInclude = false;
        let amount = 0;
        
        // 硬件选择（第1行或第2行）
        if (index === 0 && this.hardwareSelection === 1) {
          shouldInclude = true;
        } else if (index === 1 && this.hardwareSelection === 2) {
          shouldInclude = true;
        }
        // 必选项（第3行和第4行）
        else if (index === 2 && this.excelRows[2].required) {
          shouldInclude = true;
        } else if (index === 3 && this.excelRows[3].required) {
          shouldInclude = true;
        }
        // 耗材选择（第5行或第6行）
        else if (index === 4 && this.consumableSelection === 5) {
          shouldInclude = true;
        } else if (index === 5 && this.consumableSelection === 6 && row.col5 !== '按需') {
          shouldInclude = true;
        }
        
        if (shouldInclude && row.col7 !== undefined && row.col7 !== '') {
          amount = parseFloat(row.col7) || 0;
        }
        
        total += amount;
      });
      
      // 更新合计行
      this.excelRows[7].col7 = total;
    }
  }
}
</script>

<style scoped>
.pricing-section {
  padding: 100px 0;
  background: white;
}

.pricing-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.pricing-title {
  text-align: center;
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 60px;
  color: #1c1c1c;
}

.pricing-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  margin-top: 40px;
}

.pricing-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 40px;
  position: relative;
  transition: transform 0.3s, box-shadow 0.3s;
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.pricing-card.popular {
  border-color: rgb(96,166,255);
  transform: scale(1.05);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: rgb(96,166,255);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.card-header {
  margin-bottom: 24px;
}

.card-type {
  font-size: 1.3rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-header h3 {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 8px 0 -14px 0;
  color: #1c1c1c;
  line-height: 1.3;
}

.card-header p {
  color: #6c757d;
  line-height: 1.6;
}

.card-price {
  margin-bottom: 24px;
}

.price {
  font-size: 3rem;
  font-weight: 700;
  color: #1c1c1c;
}

.period {
  font-size: 1.3rem;
  color: #6c757d;
}

.card-button {
  width: 100%;
  padding: 16px;
  border: 2px solid rgb(96,166,255);
  background: transparent;
  color: rgb(96,166,255);
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 24px;
  margin-top: auto;
}

.card-button:hover {
  background: rgb(96,166,255);
  color: white;
}

.card-button.primary {
  background: rgb(96,166,255);
  color: white;
}

.card-button.primary:hover {
  background: rgb(76,146,235);
}

.card-features {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
}

.card-features li {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 0;
  color: #1c1c1c;
  font-size: 1.2rem;
  line-height: 1.5;
  text-align: left;
}

.card-features li svg {
  width: 22px;
  height: 22px;
  min-width: 22px;
  min-height: 22px;
  flex-shrink: 0;
  display: block;
}

.excel-table-container {
  margin: 60px auto 0 auto;
  max-width: 1280px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(96,166,255,0.08);
  padding: 32px 24px 24px 24px;
}
.excel-table-title {
  font-size: 3.5rem;
  font-weight: 600;
  color: #1c1c1c;
  margin-bottom: 16px;
}
.center-title {
  text-align: center;
}
.contact-info-block {
  background: #fff;
  color: #222;
  font-size: 1.15rem;
  padding: 12px 18px 12px 18px;
  margin-bottom: 18px;
  margin-top: 0;
  border-radius: 4px;
  width: fit-content;
  line-height: 1.8;
  text-align: left;
}
.remark-block {
  background: #fff;
  color: #222;
  font-size: 1.15rem;
  padding: 14px 18px 14px 18px;
  margin-top: 18px;
  border-radius: 4px;
  width: 100%;
  line-height: 1.8;
  text-align: left;
}

/* 隐藏单选框的标签文字 */
:deep(.el-radio__label) {
  display: none !important;
}

/* 数量输入框样式 */
:deep(.el-input__inner) {
  text-align: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  height: 32px;
  line-height: 32px;
  font-size: 1.15rem !important;
}

:deep(.el-input__inner:focus) {
  border-color: rgb(96,166,255);
  box-shadow: 0 0 0 2px rgba(96,166,255,0.2);
}

:deep(.el-input) {
  width: 100%;
}

:deep(.el-input .el-input__wrapper) {
  padding: 0 8px;
}

:deep(.el-table__cell) {
  font-size: 1.15rem !important;
}
</style>