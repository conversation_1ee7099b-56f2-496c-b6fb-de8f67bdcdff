import { createRouter, createWebHistory } from 'vue-router'
import Home from './components/Home.vue'
import Product from './components/Product.vue'
import Education from './components/Education.vue'
import Price from './components/Price.vue'
import Contact from './components/Contact.vue'

const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/product', name: 'Product', component: Product },
  { path: '/education', name: 'Education', component: Education },
  { path: '/price', name: 'Price', component: Price },
  { path: '/contact', name: 'Contact', component: Contact },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router 